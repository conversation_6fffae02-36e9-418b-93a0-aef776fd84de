-- ============================================================================
-- STEP 1: FIRST RUN THIS TO CHECK CURRENT PERMISSIONS
-- ============================================================================
SELECT
    schemaname,
    tablename,
    hasinserts,
    hasselects,
    hasupdates,
    hasdeletes
FROM pg_tables
LEFT JOIN information_schema.table_privileges ON table_name = tablename
WHERE tablename = 'teams';

-- Check current RLS policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies
WHERE tablename = 'teams';

-- ============================================================================
-- STEP 2: IF PERMISSIONS LOOK GOOD, RUN THESE COLUMN ADDITIONS
-- ============================================================================

-- Add industry column for studio categorization
ALTER TABLE public.teams
ADD COLUMN IF NOT EXISTS industry TEXT;

-- Add business model as JSONB for flexible business model information
ALTER TABLE public.teams
ADD COLUMN IF NOT EXISTS business_model JSONB DEFAULT '{"type": "collaborative"}';

-- Add legal entity information as JSONB
ALTER TABLE public.teams
ADD COLUMN IF NOT EXISTS legal_entity_info JSONB DEFAULT '{}';

-- Add business address as JSONB
ALTER TABLE public.teams
ADD COLUMN IF NOT EXISTS business_address JSONB DEFAULT '{}';

-- Add contact information as JSONB
ALTER TABLE public.teams
ADD COLUMN IF NOT EXISTS contact_information JSONB DEFAULT '{}';

-- ============================================================================
-- STEP 3: CREATE INDEXES FOR PERFORMANCE
-- ============================================================================

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_teams_industry ON public.teams(industry) WHERE industry IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_teams_business_model_gin ON public.teams USING GIN (business_model);
CREATE INDEX IF NOT EXISTS idx_teams_legal_entity_info_gin ON public.teams USING GIN (legal_entity_info);
CREATE INDEX IF NOT EXISTS idx_teams_business_address_gin ON public.teams USING GIN (business_address);
CREATE INDEX IF NOT EXISTS idx_teams_contact_information_gin ON public.teams USING GIN (contact_information);

-- ============================================================================
-- STEP 4: FIX RLS POLICY IF NEEDED
-- ============================================================================

-- Drop and recreate the teams insert policy to ensure it works
DROP POLICY IF EXISTS "Teams can be created by authenticated users" ON public.teams;

CREATE POLICY "Teams can be created by authenticated users" ON public.teams
    FOR INSERT
    WITH CHECK (auth.uid() = created_by);

-- Also ensure select policy exists
DROP POLICY IF EXISTS "Teams are viewable by members" ON public.teams;

CREATE POLICY "Teams are viewable by members" ON public.teams
    FOR SELECT
    USING (
        auth.uid() = created_by OR
        auth.uid() IN (
            SELECT user_id FROM team_members WHERE team_id = teams.id
        )
    );

-- ============================================================================
-- STEP 5: VERIFY EVERYTHING WORKS
-- ============================================================================

-- Test insert (replace with your actual user ID)
-- INSERT INTO public.teams (name, description, created_by, studio_type)
-- VALUES ('Test Studio', 'Test Description', auth.uid(), 'solo');

-- If the above works, delete the test record:
-- DELETE FROM public.teams WHERE name = 'Test Studio' AND created_by = auth.uid();
