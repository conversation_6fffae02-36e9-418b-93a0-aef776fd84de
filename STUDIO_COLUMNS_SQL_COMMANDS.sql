-- ============================================================================
-- DIRECT SQL COMMANDS FOR SUPABASE SQL EDITOR
-- Copy and paste these commands into your Supabase SQL Editor
-- ============================================================================

-- Add industry column for studio categorization
ALTER TABLE public.teams 
ADD COLUMN IF NOT EXISTS industry TEXT;

-- Add business model as JSONB for flexible business model information
ALTER TABLE public.teams 
ADD COLUMN IF NOT EXISTS business_model JSONB DEFAULT '{"type": "collaborative"}';

-- Add legal entity information as JSONB
ALTER TABLE public.teams 
ADD COLUMN IF NOT EXISTS legal_entity_info JSONB DEFAULT '{}';

-- Add business address as JSONB
ALTER TABLE public.teams 
ADD COLUMN IF NOT EXISTS business_address JSONB DEFAULT '{}';

-- Add contact information as JSONB
ALTER TABLE public.teams 
ADD COLUMN IF NOT EXISTS contact_information JSONB DEFAULT '{}';

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_teams_industry ON public.teams(industry) WHERE industry IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_teams_business_model_gin ON public.teams USING GIN (business_model);
CREATE INDEX IF NOT EXISTS idx_teams_legal_entity_info_gin ON public.teams USING GIN (legal_entity_info);
CREATE INDEX IF NOT EXISTS idx_teams_business_address_gin ON public.teams USING GIN (business_address);
CREATE INDEX IF NOT EXISTS idx_teams_contact_information_gin ON public.teams USING GIN (contact_information);

-- Add validation constraint
ALTER TABLE public.teams 
ADD CONSTRAINT IF NOT EXISTS teams_business_model_type_check 
CHECK (business_model ? 'type');
